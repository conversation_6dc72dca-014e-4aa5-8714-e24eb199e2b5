import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts';

interface TooltipPayload {
  name: string;
  value: number;
  color?: string;
}

const CustomPieTooltip = ({ active, payload }: { active?: boolean; payload?: TooltipPayload[] }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm font-medium text-gray-900">
          {payload[0].name}: {payload[0].value}%
        </p>
      </div>
    );
  }
  return null;
};

const VerificationChart = () => {
  const verificationData = [
    { name: 'Verified', value: 75, color: '#1A4488' },
    { name: 'Unverified', value: 25, color: '#CBD5E0' }
  ];

  return (
    <div className="bg-white p-6 rounded-xl border border-gray-200">
      <h3 className="text-lg border-b pb-2 font-medium text-gray-900 mb-4">Verified vs. Unverified</h3>
      <div className="h-56 flex items-center justify-center">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={verificationData}
              cx="50%"
              cy="50%"
              innerRadius={50}
              outerRadius={70}
              paddingAngle={5}
              dataKey="value"
            >
              {verificationData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomPieTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </div>
      <div className="flex items-center justify-center gap-4 mt-4">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-[#1A4488] rounded"></div>
          <span className="text-sm text-gray-600">Verified</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-[#CBD5E0] rounded"></div>
          <span className="text-sm text-gray-600">Unverified</span>
        </div>
      </div>
    </div>
  );
};

export default VerificationChart;