import { ChevronRight } from 'lucide-react';

const TopInvestors = () => {
  const topInvestors = [
    { name: 'SolarGrid Africa', investment: '$350K', color: '#f59e0b', initial: 'S' },
    { name: 'Bright Future Solar', investment: '$500K', color: '#3b82f6', initial: 'B' },
    { name: 'EcoBattery Tech', investment: '$200K', color: '#8b5cf6', initial: 'E' },
    { name: 'EcoBattery Tech', investment: '$150K', color: '#8b5cf6', initial: 'E' }
  ];

  return (
    <div className="bg-white p-6 rounded-xl border border-gray-200">
      <div className="border-b border-gray-200 pb-4 mb-4">
        <h3 className="text-lg font-medium text-gray-900">Top Investors (by capital)</h3>
      </div>
      <div className="space-y-3 max-h-80 overflow-y-auto pr-2 custom-scrollbar">
        {topInvestors.map((investor, index) => (
          <div key={index} className="flex items-center gap-3 p-3 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors">
            <div 
              className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
              style={{ backgroundColor: investor.color }}
            >
              {investor.initial}
            </div>
            <div className="flex-1">
              <div className="text-lg font-medium text-gray-900">{investor.name}</div>
              <div className="text-sm text-gray-500">{investor.investment} Investment</div>
            </div>
            <button className="text-orange-500 text-sm hover:text-orange-600 flex items-center gap-1">
              View <ChevronRight className="w-3 h-3" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TopInvestors;