import { TrendingUp, ChevronRight } from 'lucide-react';

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: string;
  viewAll?: boolean;
  viewAllText?: string;
  onClick?: () => void;
}

const MetricCard = ({ title, value, subtitle, trend, viewAll = false, viewAllText = "View all", onClick }: MetricCardProps) => (
  <div className="bg-white p-6 rounded-xl border border-gray-200 hover:shadow-md transition-shadow">
    <div className="mb-2">
      <h3 className="text-lg font-medium text-black border-b border-gray-200 pb-2">{title}</h3>
    </div>
    <div className="text-2xl font-bold text-gray-900 mb-1">{value}</div>
    {subtitle && <div className="text-sm text-gray-500">{subtitle}</div>}
    {viewAll && (
      <button
        onClick={onClick}
        className="text-orange-500 text-sm hover:text-orange-600 flex items-center gap-1 mt-2 cursor-pointer"
      >
        {viewAllText} <ChevronRight className="w-3 h-3" />
      </button>
    )}
    {trend && (
      <div className="flex items-center gap-1 mt-2">
        <TrendingUp className="w-4 h-4 text-green-500" />
        <span className="text-sm text-gray-600">{trend}</span>
      </div>
    )}
  </div>
);

export default MetricCard;