import { Bookmark } from 'lucide-react';

const TopProjects = () => {
  const topProjects = [
    { 
      name: '7MWp PV + 6MWh BESS Re...', 
      funding: '$200K', 
      type: 'Equity',
      status: 'active'
    },
    { 
      name: '100 MW wind farm hybrid in...', 
      funding: '$200K', 
      type: 'Agriculture',
      status: 'active'
    },
    { 
      name: '10 MW Early Stage Distribut...', 
      funding: '$200K', 
      type: 'Energy',
      status: 'active'
    }
  ];

  return (
    <div className="bg-white p-6 rounded-xl border border-gray-200">
      <div className="border-b border-gray-200 pb-4 mb-4">
        <h3 className="text-lg font-medium text-gray-900">Top Performing Projects</h3>
      </div>
      <div className="space-y-3 max-h-80 overflow-y-auto pr-2 custom-scrollbar">
        {topProjects.map((project, index) => (
          <div key={index} className="p-3 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors">
            <div className="flex items-start justify-between mb-2">
              <div className="text-lg font-medium text-gray-900 flex-1 pr-2">
                {project.name}
              </div>
              <Bookmark className="w-4 h-4 text-gray-400 cursor-pointer hover:text-gray-600" />
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <span>Funding Sought: {project.funding}</span>
            </div>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm text-gray-500">Investment type:</span>
              <span className="text-sm text-orange-500">{project.type}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TopProjects;