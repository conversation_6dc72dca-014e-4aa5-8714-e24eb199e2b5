# lodash.memoize v4.1.2

The [lodash](https://lodash.com/) method `_.memoize` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.memoize
```

In Node.js:
```js
var memoize = require('lodash.memoize');
```

See the [documentation](https://lodash.com/docs#memoize) or [package source](https://github.com/lodash/lodash/blob/4.1.2-npm-packages/lodash.memoize) for more details.
