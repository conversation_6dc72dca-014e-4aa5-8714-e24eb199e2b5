# lodash.reduce v4.6.0

The [lodash](https://lodash.com/) method `_.reduce` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.reduce
```

In Node.js:
```js
var reduce = require('lodash.reduce');
```

See the [documentation](https://lodash.com/docs#reduce) or [package source](https://github.com/lodash/lodash/blob/4.6.0-npm-packages/lodash.reduce) for more details.
