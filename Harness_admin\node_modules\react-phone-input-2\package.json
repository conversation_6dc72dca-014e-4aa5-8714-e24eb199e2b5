{"name": "react-phone-input-2", "version": "2.15.1", "description": "A react component to format phone numbers", "main": "lib/lib.js", "typings": "index.d.ts", "scripts": {"test": "jest", "start": "TARGET=dev_js NODE_ENV=development webpack-dev-server --progress", "start:css": "TARGET=dev_css NODE_ENV=development webpack-dev-server --progress", "build": "export SET NODE_OPTIONS=--openssl-legacy-provider && npm run build:js && npm run build:css", "build:js": "TARGET=build_js NODE_ENV=production webpack -p --progress", "build:css": "TARGET=build_css NODE_ENV=production webpack -p --progress && rm ./main.js", "prepublishOnly": "npm run build", "analyze": "TARGET=analyze NODE_ENV=production webpack -p --progress"}, "repository": {"type": "git", "url": "git+https://github.com/bl00mber/react-phone-input-2.git"}, "keywords": ["react", "phone", "format", "number", "input", "telephone", "javascript", "international", "tel", "localized", "material", "bootstrap", "i18n"], "files": ["lang", "lib", "LICENSE", "README.md", "index.d.ts"], "author": "<PERSON> <<EMAIL>> (https://github.com/bl00mber)", "contributors": ["<PERSON><PERSON> <<EMAIL>> (https://github.com/razagill)"], "license": "MIT", "bugs": {"url": "https://github.com/bl00mber/react-phone-input-2/issues"}, "homepage": "https://github.com/bl00mber/react-phone-input-2", "jest": {"moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2)$": "<rootDir>/test/__mocks__/fileMock.js", "\\.(css|less|stylus|scss)$": "<rootDir>/test/__mocks__/styleMock.js"}, "globals": {"__DEV__": false}}, "devDependencies": {"@babel/core": "^7.3.3", "babel-loader": "^8.0.5", "babel-preset-react-app": "^7.0.1", "css-loader": "^2.1.0", "extract-loader": "^3.1.0", "file-loader": "^5.0.2", "jest": "^24.7.0", "less": "^3.9.0", "less-loader": "^4.1.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-hot-loader": "^4.6.5", "react-testing-library": "^6.0.4", "style-loader": "^0.23.1", "url-loader": "^1.1.2", "webpack": "^4.29.5", "webpack-bundle-analyzer": "^3.0.4", "webpack-cli": "^3.2.3", "webpack-dev-server": "^3.1.14", "webpack-merge": "^4.2.1"}, "peerDependencies": {"react": "^16.12.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^20.0.0 || ^21.0.0", "react-dom": "^16.12.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^20.0.0 || ^21.0.0"}, "dependencies": {"classnames": "^2.2.6", "lodash.debounce": "^4.0.8", "lodash.memoize": "^4.1.2", "lodash.reduce": "^4.6.0", "lodash.startswith": "^4.2.1", "prop-types": "^15.7.2"}}