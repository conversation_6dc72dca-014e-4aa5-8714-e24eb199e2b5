import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid } from 'recharts';

interface ChartData {
  month: string;
  developers?: number;
  investors?: number;
  [key: string]: string | number | undefined;
}

const CustomLineTooltip = ({ active, payload, label }: {
  active?: boolean;
  payload?: Array<{
    color: string;
    dataKey: string;
    value: number;
  }>;
  label?: string;
}) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm font-medium text-gray-900 mb-1">{label}</p>
        {payload.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.dataKey === 'developers' ? 'Developers' : 'Investors'}: {entry.value.toLocaleString()}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const LineChartComponent = ({ data, dataKey, selected<PERSON>onth, chartType }: {
  data: ChartData[];
  dataKey: string;
  selectedMonth: string;
  chartType: string;
}) => (
  <div className="h-64">
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={data} key={`${chartType}-${selectedMonth}`}>
        <XAxis 
          dataKey="month" 
          axisLine={true}
          tickLine={true}
          tick={{ fontSize: 12, fill: '#6b7280' }}
          stroke="#e5e7eb"
        />
        <YAxis
          axisLine={true}
          tickLine={true}
          tick={{ fontSize: 12, fill: '#6b7280' }}
          stroke="#e5e7eb"
          domain={[0, 1500]}
          ticks={[0, 300, 600, 900, 1200, 1500]}
        />
        <CartesianGrid
          strokeDasharray="3 3"
          stroke="#f3f4f6"
          horizontal={true}
          vertical={false}
        />
        <Tooltip content={<CustomLineTooltip />} />
        <Line 
          type="monotone" 
          dataKey={dataKey} 
          stroke="#f59e0b" 
          strokeWidth={2}
          dot={false}
        />
        <defs>
          <linearGradient id={`${chartType}Gradient`} x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.1}/>
            <stop offset="95%" stopColor="#f59e0b" stopOpacity={0}/>
          </linearGradient>
        </defs>
      </LineChart>
    </ResponsiveContainer>
  </div>
);

export default LineChartComponent;